import React from "react";
import { X } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import IncidentReportForm from "./IncidentReportForm";

interface NewIncidentDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onIncidentCreated?: (incident: any) => void;
}

const NewIncidentDialog: React.FC<NewIncidentDialogProps> = ({
  open,
  onOpenChange,
  onIncidentCreated,
}) => {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl w-[90vw] max-h-[90vh] overflow-y-auto p-0">
        <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </DialogClose>
        <DialogHeader className="p-6 pb-0">
          <DialogTitle className="text-2xl font-bold">Report an Incident</DialogTitle>
          <DialogDescription>
            Please complete this form to report any incidents or near misses.
          </DialogDescription>
        </DialogHeader>
        <div className="px-6 pb-6">
          <IncidentReportForm
            onSuccess={(incidentData) => {
              if (incidentData && onIncidentCreated) {
                onIncidentCreated(incidentData);
              }
              onOpenChange(false);
            }}
            isDialog={true}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default NewIncidentDialog;
